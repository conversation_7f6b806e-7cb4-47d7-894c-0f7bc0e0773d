using System;
using System.Collections;
using Game.Interfaces;
using Game.Managers;
using SmartVertex.Tools;
using UnityEngine;

namespace Game.CommandSystem
{
    /// <summary>
    /// Command to move an object to a target position over a duration using damping.
    /// </summary>
    public class MoveCommand : ICoroutineCommand
    {
        /// <summary>Parameters for the move command.</summary>
        public MoveParams Parameters { get; }

        /// <summary>Creates a new MoveCommand.</summary>
        /// <param name="parameters">Move parameters.</param>
        public MoveCommand(MoveParams parameters)
        {
            Parameters = parameters;
        }

        /// <inheritdoc/>
        public IEnumerator Execute()
        {
            string sceneId = ObjectManager.Instance.GetSceneIdForAnyObject(Parameters.objectId);
            GameObject sceneObject = ObjectManager.Instance.FindScene(sceneId);
            GameObject selectedObject = ObjectManager.Instance.FindById(Parameters.objectId);
            Vector2Int gridSize = ObjectManager.Instance.SceneGridSize;

            Vector3 newPosition = GridUtility.GetWorldPosition
            (sceneObject, gridSize, Parameters.targetPosition, GridUtility.GridPlane.XY);

            if (selectedObject == null)
            {
                Debug.LogWarning($"Object with ID '{Parameters.objectId}' not found for move command.");
                yield break;
            }

            IMovable movable = selectedObject.GetComponentInChildren<IMovable>();
            if (movable == null)
            {
                Debug.LogWarning($"Object with ID '{Parameters.objectId}' does not implement IMovable.");
                yield break;
            }

            yield return movable.Move(newPosition, Parameters.damping);
        }
    }

    /// <summary>Parameters for Move command.</summary>
    [Serializable]
    public struct MoveParams
    {
        /// <summary>The unique identifier of the object to move.</summary>
        public string objectId;

        /// <summary>The target grid position to move to.</summary>
        public Vector2Int targetPosition;

        /// <summary>The damping time for the movement transition.</summary>
        public float damping;
    }
}
