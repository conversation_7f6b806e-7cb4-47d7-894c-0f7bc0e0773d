using UnityEngine;
using Game.Data;
using UnityEngine.Events;

namespace Game.UI
{
    /// <summary>
    /// Runtime GUI for configuring GeneratorConfig data using Unity's immediate mode GUI.
    /// </summary>
    public class RuntimeConfigGUI : MonoBehaviour
    {
        #region Fields

        [Header("GUI Settings")]
        [SerializeField] private bool showGUI = true;
        [SerializeField] private Rect windowRect = new Rect(20, 20, 400, 600);
        [SerializeField] private Vector2 scrollPosition = Vector2.zero;

        [Header("Events")]
        public UnityEvent<GeneratorConfig> onConfigSubmitted;

        // GUI input fields
        private string geminiApiKey = "";
        private string geminiModel = "gemini-2.5-flash";
        private string geminiSystemInstruction = "You are a helpful AI assistant.";
        private string geminiTemperature = "1.0";
        private string geminiTopP = "0.95";
        private string geminiTopK = "40";
        private string geminiThinkingBudget = "-1";

        private string openAIApiKey = "";
        private string openAIApiUrl = "https://api.openai.com/v1/audio/speech";
        private string openAIModel = "tts-1";

        private string assetAddresses = "";
        private string scenario = "";

        // GUI style variables
        private GUIStyle windowStyle;
        private GUIStyle labelStyle;
        private GUIStyle textFieldStyle;
        private GUIStyle buttonStyle;
        private GUIStyle errorStyle;
        private bool stylesInitialized = false;

        // Validation error message
        private string validationErrorMessage = "";

        #endregion

        #region Properties

        /// <summary>
        /// Gets or sets whether the GUI is visible.
        /// </summary>
        public bool ShowGUI
        {
            get => showGUI;
            set => showGUI = value;
        }

        #endregion

        #region Unity Lifecycle

        private void Start()
        {
            // Initialize event if not assigned
            if (onConfigSubmitted == null)
            {
                onConfigSubmitted = new UnityEvent<GeneratorConfig>();
            }

            // Try to load saved configuration, fallback to defaults if none exists
            if (HasSavedConfiguration())
            {
                LoadSavedConfiguration();
            }
            else
            {
                LoadDefaultValues();
            }
        }

        private void OnGUI()
        {
            if (!showGUI) return;

            InitializeStyles();
            windowRect = GUI.Window(0, windowRect, DrawConfigWindow, "Animation Generator Config", windowStyle);
        }

        #endregion

        #region GUI Drawing

        private void DrawConfigWindow(int windowID)
        {
            GUILayout.BeginVertical();

            scrollPosition = GUILayout.BeginScrollView(scrollPosition, GUILayout.Height(550));

            DrawGeminiSection();
            GUILayout.Space(10);
            DrawOpenAISection();
            GUILayout.Space(10);
            DrawGeneralSection();

            GUILayout.EndScrollView();

            GUILayout.Space(10);
            DrawButtons();

            GUILayout.EndVertical();

            GUI.DragWindow();
        }

        private void DrawGeminiSection()
        {
            GUILayout.Label("Gemini Configuration", labelStyle);

            GUILayout.Label("API Key:");
            geminiApiKey = GUILayout.TextField(geminiApiKey, textFieldStyle);

            GUILayout.Label("Model:");
            geminiModel = GUILayout.TextField(geminiModel, textFieldStyle);

            GUILayout.Label("System Instruction:");
            geminiSystemInstruction = GUILayout.TextArea(geminiSystemInstruction, GUILayout.Height(60));

            GUILayout.Label("Temperature (0.0 - 2.0):");
            geminiTemperature = GUILayout.TextField(geminiTemperature, textFieldStyle);

            GUILayout.Label("Top P (0.0 - 1.0):");
            geminiTopP = GUILayout.TextField(geminiTopP, textFieldStyle);

            GUILayout.Label("Top K:");
            geminiTopK = GUILayout.TextField(geminiTopK, textFieldStyle);

            GUILayout.Label("Thinking Budget (-1 for unlimited):");
            geminiThinkingBudget = GUILayout.TextField(geminiThinkingBudget, textFieldStyle);
        }

        private void DrawOpenAISection()
        {
            GUILayout.Label("OpenAI Text-to-Speech Configuration", labelStyle);

            GUILayout.Label("API Key:");
            openAIApiKey = GUILayout.TextField(openAIApiKey, textFieldStyle);

            GUILayout.Label("API URL:");
            openAIApiUrl = GUILayout.TextField(openAIApiUrl, textFieldStyle);

            GUILayout.Label("Model:");
            openAIModel = GUILayout.TextField(openAIModel, textFieldStyle);
        }

        private void DrawGeneralSection()
        {
            GUILayout.Label("General Configuration", labelStyle);

            GUILayout.Label("Asset Addresses:");
            assetAddresses = GUILayout.TextArea(assetAddresses, GUILayout.Height(60));

            GUILayout.Label("Scenario:");
            scenario = GUILayout.TextArea(scenario, GUILayout.Height(80));
        }

        private void DrawButtons()
        {
            // Display validation error message if any
            if (!string.IsNullOrEmpty(validationErrorMessage))
            {
                GUILayout.Space(10);
                GUILayout.Label("Validation Error:", errorStyle);
                GUILayout.Label(validationErrorMessage, errorStyle);
                GUILayout.Space(10);
            }

            GUILayout.BeginHorizontal();

            if (GUILayout.Button("Reset to Defaults", buttonStyle))
            {
                LoadDefaultValues();
                validationErrorMessage = ""; // Clear error message
            }

            if (GUILayout.Button("Save Config", buttonStyle))
            {
                SaveConfiguration();
            }

            GUILayout.EndHorizontal();

            GUILayout.BeginHorizontal();

            if (GUILayout.Button("Load Config", buttonStyle))
            {
                LoadSavedConfiguration();
                validationErrorMessage = ""; // Clear error message
            }

            if (GUILayout.Button("Submit Configuration", buttonStyle))
            {
                SubmitConfiguration();
            }

            GUILayout.EndHorizontal();
        }

        #endregion

        #region Configuration Management

        private void LoadDefaultValues()
        {
            var defaultConfig = new GeneratorConfig
            {
                geminiConfig = new GeminiConfig(),
                openAITextToSpeechConfig = new OpenAITextToSpeechConfig(),
                assetAddresses = "",
                scenario = ""
            };

            SetFieldsFromConfig(defaultConfig);
        }

        private void SetFieldsFromConfig(GeneratorConfig config)
        {
            if (config.geminiConfig != null)
            {
                geminiApiKey = config.geminiConfig.apiKey ?? "";
                geminiModel = config.geminiConfig.model;
                geminiSystemInstruction = config.geminiConfig.systemInstruction;
                geminiTemperature = config.geminiConfig.temperature.ToString("F2");
                geminiTopP = config.geminiConfig.topP.ToString("F2");
                geminiTopK = config.geminiConfig.topK.ToString();
                geminiThinkingBudget = config.geminiConfig.thinkingBudget.ToString();
            }

            if (config.openAITextToSpeechConfig != null)
            {
                openAIApiKey = config.openAITextToSpeechConfig.apiKey ?? "";
                openAIApiUrl = config.openAITextToSpeechConfig.apiUrl;
                openAIModel = config.openAITextToSpeechConfig.model;
            }

            assetAddresses = config.assetAddresses ?? "";
            scenario = config.scenario ?? "";
        }

        private GeneratorConfig CreateConfigFromFields()
        {
            var config = new GeneratorConfig
            {
                geminiConfig = new GeminiConfig
                {
                    apiKey = geminiApiKey,
                    model = geminiModel,
                    systemInstruction = geminiSystemInstruction,
                    temperature = ParseFloat(geminiTemperature, 1.0f),
                    topP = ParseFloat(geminiTopP, 0.95f),
                    topK = ParseInt(geminiTopK, 40),
                    thinkingBudget = ParseInt(geminiThinkingBudget, -1)
                },
                openAITextToSpeechConfig = new OpenAITextToSpeechConfig
                {
                    apiKey = openAIApiKey,
                    apiUrl = openAIApiUrl,
                    model = openAIModel
                },
                assetAddresses = assetAddresses,
                scenario = scenario
            };

            return config;
        }

        private void SubmitConfiguration()
        {
            // Validate input data
            string validationError = ValidateConfiguration();
            if (!string.IsNullOrEmpty(validationError))
            {
                validationErrorMessage = validationError;
                Debug.LogWarning($"Configuration validation failed: {validationError}");
                return;
            }

            // Clear any previous error message
            validationErrorMessage = "";

            var config = CreateConfigFromFields();

            onConfigSubmitted?.Invoke(config);
            Debug.Log("Configuration submitted successfully!");

            // Hide the panel after successful submission
            showGUI = false;
        }

        /// <summary>
        /// Validates the current configuration data.
        /// </summary>
        /// <returns>Error message if validation fails, null or empty if validation passes.</returns>
        private string ValidateConfiguration()
        {
            // Validate Gemini API Key
            if (string.IsNullOrWhiteSpace(geminiApiKey))
            {
                return "Gemini API Key is required.";
            }

            // Validate Gemini Model
            if (string.IsNullOrWhiteSpace(geminiModel))
            {
                return "Gemini Model is required.";
            }

            // Validate Gemini System Instruction
            if (string.IsNullOrWhiteSpace(geminiSystemInstruction))
            {
                return "Gemini System Instruction is required.";
            }

            // Validate Gemini Temperature
            if (!float.TryParse(geminiTemperature, out float temperature) || temperature < 0.0f || temperature > 2.0f)
            {
                return "Gemini Temperature must be a number between 0.0 and 2.0.";
            }

            // Validate Gemini Top P
            if (!float.TryParse(geminiTopP, out float topP) || topP < 0.0f || topP > 1.0f)
            {
                return "Gemini Top P must be a number between 0.0 and 1.0.";
            }

            // Validate Gemini Top K
            if (!int.TryParse(geminiTopK, out int topK) || topK < 1)
            {
                return "Gemini Top K must be a positive integer.";
            }

            // Validate Gemini Thinking Budget
            if (!int.TryParse(geminiThinkingBudget, out int thinkingBudget) || thinkingBudget < -1)
            {
                return "Gemini Thinking Budget must be -1 (unlimited) or a positive integer.";
            }

            // Validate OpenAI API Key
            if (string.IsNullOrWhiteSpace(openAIApiKey))
            {
                return "OpenAI API Key is required.";
            }

            // Validate OpenAI API URL
            if (string.IsNullOrWhiteSpace(openAIApiUrl))
            {
                return "OpenAI API URL is required.";
            }

            // Validate OpenAI API URL format
            if (!System.Uri.TryCreate(openAIApiUrl, System.UriKind.Absolute, out _))
            {
                return "OpenAI API URL must be a valid URL.";
            }

            // Validate OpenAI Model
            if (string.IsNullOrWhiteSpace(openAIModel))
            {
                return "OpenAI Model is required.";
            }

            // Validate Asset Addresses
            if (string.IsNullOrWhiteSpace(assetAddresses))
            {
                return "Asset Addresses are required.";
            }

            // Validate Scenario
            if (string.IsNullOrWhiteSpace(scenario))
            {
                return "Scenario is required.";
            }

            // All validations passed
            return null;
        }

        private void SaveConfiguration()
        {
            try
            {
                // Save Gemini configuration
                PlayerPrefs.SetString("AnimGen_GeminiApiKey", geminiApiKey);
                PlayerPrefs.SetString("AnimGen_GeminiModel", geminiModel);
                PlayerPrefs.SetString("AnimGen_GeminiSystemInstruction", geminiSystemInstruction);
                PlayerPrefs.SetString("AnimGen_GeminiTemperature", geminiTemperature);
                PlayerPrefs.SetString("AnimGen_GeminiTopP", geminiTopP);
                PlayerPrefs.SetString("AnimGen_GeminiTopK", geminiTopK);
                PlayerPrefs.SetString("AnimGen_GeminiThinkingBudget", geminiThinkingBudget);

                // Save OpenAI configuration
                PlayerPrefs.SetString("AnimGen_OpenAIApiKey", openAIApiKey);
                PlayerPrefs.SetString("AnimGen_OpenAIApiUrl", openAIApiUrl);
                PlayerPrefs.SetString("AnimGen_OpenAIModel", openAIModel);

                // Save general configuration
                PlayerPrefs.SetString("AnimGen_AssetAddresses", assetAddresses);
                PlayerPrefs.SetString("AnimGen_Scenario", scenario);

                PlayerPrefs.Save();
                Debug.Log("Configuration saved successfully!");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to save configuration: {e.Message}");
            }
        }

        private void LoadSavedConfiguration()
        {
            try
            {
                // Load Gemini configuration
                geminiApiKey = PlayerPrefs.GetString("AnimGen_GeminiApiKey", "");
                geminiModel = PlayerPrefs.GetString("AnimGen_GeminiModel", "gemini-2.5-flash");
                geminiSystemInstruction = PlayerPrefs.GetString("AnimGen_GeminiSystemInstruction", "You are a helpful AI assistant.");
                geminiTemperature = PlayerPrefs.GetString("AnimGen_GeminiTemperature", "1.0");
                geminiTopP = PlayerPrefs.GetString("AnimGen_GeminiTopP", "0.95");
                geminiTopK = PlayerPrefs.GetString("AnimGen_GeminiTopK", "40");
                geminiThinkingBudget = PlayerPrefs.GetString("AnimGen_GeminiThinkingBudget", "-1");

                // Load OpenAI configuration
                openAIApiKey = PlayerPrefs.GetString("AnimGen_OpenAIApiKey", "");
                openAIApiUrl = PlayerPrefs.GetString("AnimGen_OpenAIApiUrl", "https://api.openai.com/v1/audio/speech");
                openAIModel = PlayerPrefs.GetString("AnimGen_OpenAIModel", "tts-1");

                // Load general configuration
                assetAddresses = PlayerPrefs.GetString("AnimGen_AssetAddresses", "");
                scenario = PlayerPrefs.GetString("AnimGen_Scenario", "");

                Debug.Log("Configuration loaded successfully!");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to load configuration: {e.Message}");
                LoadDefaultValues(); // Fallback to defaults if loading fails
            }
        }

        private bool HasSavedConfiguration()
        {
            // Check if any saved configuration exists by looking for a key configuration value
            return PlayerPrefs.HasKey("AnimGen_GeminiModel");
        }

        #endregion

        #region Utility Methods

        private float ParseFloat(string value, float defaultValue)
        {
            return float.TryParse(value, out float result) ? result : defaultValue;
        }

        private int ParseInt(string value, int defaultValue)
        {
            return int.TryParse(value, out int result) ? result : defaultValue;
        }

        private void InitializeStyles()
        {
            if (stylesInitialized) return;

            windowStyle = new GUIStyle(GUI.skin.window)
            {
                fontSize = 16,
                fontStyle = FontStyle.Bold
            };

            labelStyle = new GUIStyle(GUI.skin.label)
            {
                fontSize = 16,
                fontStyle = FontStyle.Bold,
                margin = new RectOffset(0, 0, 5, 2)
            };

            textFieldStyle = new GUIStyle(GUI.skin.textField)
            {
                fontSize = 18,
                margin = new RectOffset(0, 0, 2, 5)
            };

            buttonStyle = new GUIStyle(GUI.skin.button)
            {
                fontSize = 16,
                fontStyle = FontStyle.Bold,
                fixedHeight = 40
            };

            errorStyle = new GUIStyle(GUI.skin.label)
            {
                fontSize = 16,
                fontStyle = FontStyle.Bold,
                normal = { textColor = Color.red },
                wordWrap = true
            };

            stylesInitialized = true;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Loads configuration data into the GUI fields.
        /// </summary>
        /// <param name="config">The configuration to load.</param>
        public void LoadConfiguration(GeneratorConfig config)
        {
            if (config != null)
            {
                SetFieldsFromConfig(config);
            }
        }

        /// <summary>
        /// Gets the current configuration from the GUI fields.
        /// </summary>
        /// <returns>The current configuration.</returns>
        public GeneratorConfig GetCurrentConfiguration()
        {
            return CreateConfigFromFields();
        }

        /// <summary>
        /// Toggles the GUI visibility.
        /// </summary>
        public void ToggleGUI()
        {
            showGUI = !showGUI;
        }

        #endregion
    }
}
